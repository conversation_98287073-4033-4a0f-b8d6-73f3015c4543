<template>
  <div class="preview">
    <el-row :gutter="10">
      <el-col :span="6">
        <!-- 设备分组管理 -->
        <div class="group-box">
          <div class="group-header">
            <h3 class="group-title">监控分组</h3>
            <el-button
              type="primary"
              size="small"
              icon="Plus"
              @click="showAddGroupDialog"
            >
              新增分组
            </el-button>
          </div>

          <!-- 监控分组列表 -->
          <div class="group-list-container">
            <el-scrollbar height="300px">
              <div class="group-list">
                <div
                  v-for="group in groupListData"
                  :key="group.id"
                  class="group-item"
                  :class="{ 'active': selectedGroup?.id === group.id }"
                  @click="handleGroupClick(group)"
                >
                  <div class="group-content">
                    <div class="group-info">
                      <el-icon class="group-icon">
                        <Monitor />
                      </el-icon>
                      <div class="group-details">
                        <div class="group-name">{{ group.groupName }}</div>
                        <div class="group-desc">{{ group.remark }}</div>
                      </div>
                      <!-- <div class="device-count">
                        <el-tag size="small" type="info">{{ group.deviceCount || 0 }}台</el-tag>
                      </div> -->
                    </div>
                    <div class="group-actions" v-if="group.id !== '0'">
                      <el-button
                        type="text"
                        size="small"
                        icon="Edit"
                        @click.stop="editGroup(group)"
                        title="编辑分组"
                      />
                      <el-button
                        type="text"
                        size="small"
                        icon="Delete"
                        @click.stop="deleteGroup(group)"
                        title="删除分组"
                        :disabled="group.deviceCount > 0"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>

          <!-- 当前分组的监控设备列表 -->
          <div class="group-devices" v-if="selectedGroup">
            <div class="devices-header">
              <h4>{{ selectedGroup.name }} - 监控设备</h4>
              <el-button
                type="text"
                size="small"
                icon="Plus"
                @click="showAddDeviceDialog"
              >
                添加摄像头
              </el-button>
            </div>

            <div class="devices-list">
              <el-scrollbar height="300px">
                <div
                  v-for="device in currentGroupDevices"
                  :key="device.id"
                  class="device-item"
                  draggable="true"
                  @dragstart="handleDragStart(device, $event)"
                  @dragover.prevent
                  @drop="handleDrop($event)"
                >
                  <div class="device-info">
                    <el-icon class="device-icon">
                      <Monitor />
                    </el-icon>
                    <div class="device-details">
                      <div class="device-name">{{ device.deviceName }}</div>
                      <div class="device-meta">
                        <span class="device-no">No：{{ device.deviceNo }}</span>
                        <!-- <span class="device-location">{{ device.location }}</span>
                        <span class="device-resolution">{{ device.resolution }}</span> -->
                      </div>
                    </div>
                    <div class="device-status">
                      <el-tag
                        :type="device.status === 1 ? 'success' : 'danger'"
                        size="small"
                      >
                        {{ device.status === 1 ? '在线' : '离线' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="device-actions">
                    <el-button
                      type="text"
                      size="small"
                      icon="Right"
                      @click="showMoveDeviceDialog(device)"
                      title="移动到其他分组"
                    />
                    <el-button
                      type="text"
                      size="small"
                      icon="Delete"
                      @click="removeDeviceFromGroup(device)"
                      title="从分组中移除"
                      v-if="selectedGroup.id !== 'ungrouped'"
                    />
                  </div>
                </div>

                <el-empty
                  v-if="currentGroupDevices.length === 0"
                  description="暂无设备"
                  :image-size="80"
                />
              </el-scrollbar>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <!-- 监控分屏 -->
        <div class="monitoring-box">
          <div class="monitoring-header">
            <div class="monitoring-header-left">
              <h3>监控预览</h3>
              <div class="monitoring-info" v-if="selectedGroup">
                当前分组：{{ selectedGroup.name }} ({{ currentGroupDevices.length }} 个设备)
              </div>
            </div>
            <div class="monitoring-header-right">
              <div class="screen-split-controls">
                <span class="control-label">分屏数量：</span>
                <el-radio-group
                  v-model="selectedScreenSplit"
                  size="small"
                  @change="handleScreenSplitChange"
                >
                  <el-radio-button :value="1">1分屏</el-radio-button>
                  <el-radio-button :value="4">4分屏</el-radio-button>
                  <el-radio-button :value="6">6分屏</el-radio-button>
                  <el-radio-button :value="9">9分屏</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </div>
          <div class="monitoring-content">
            <!-- 监控画面网格布局 -->
            <div
              class="monitoring-grid"
              :class="getGridClass()"
            >
              <div
                v-for="device in displayDevices"
                :key="device.id"
                class="monitoring-item"
                @click="selectDevice(device)"
              >
                <div class="monitor-placeholder">
                  <el-icon size="40"><Monitor /></el-icon>
                  <div class="device-label">{{ device.name }}</div>
                  <div class="device-status-badge">
                    <el-tag
                      :type="device.status === 'online' ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ device.status === 'online' ? '在线' : '离线' }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 填充空白格子 -->
              <div
                v-for="i in emptySlots"
                :key="'empty-' + i"
                class="monitoring-item empty"
              >
                <div class="monitor-placeholder">
                  <el-icon size="40" color="#ddd"><Plus /></el-icon>
                  <div class="device-label">空闲</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 新增/编辑分组对话框 -->
    <el-dialog
      :title="groupDialogTitle"
      v-model="groupDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="groupForm"
        :model="groupForm"
        :rules="groupFormRules"
        label-width="80px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分组描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="groupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitGroupForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加监控设备到分组对话框 -->
    <el-dialog
      title="添加监控设备到分组"
      v-model="addDeviceDialogVisible"
      width="700px"
      :close-on-click-modal="false"
    >
      <div class="add-device-content">
        <el-input
          v-model="deviceSearchKeyword"
          placeholder="搜索摄像头名称、编号或位置"
          prefix-icon="Search"
          style="margin-bottom: 15px;"
        />
        <el-table
          ref="deviceTable"
          :data="filteredAvailableDevices"
          @selection-change="handleDeviceSelectionChange"
          height="350"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="摄像头名称" width="150" />
          <el-table-column prop="deviceNo" label="设备编号" width="120" />
          <el-table-column prop="location" label="安装位置" width="120" />
          <el-table-column prop="resolution" label="分辨率" width="80" />
          <el-table-column prop="brand" label="品牌" width="80" />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'online' ? 'success' : 'danger'"
                size="small"
              >
                {{ scope.row.status === 'online' ? '在线' : '离线' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDeviceDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="addSelectedDevicesToGroup"
            :disabled="selectedDevices.length === 0"
          >
            添加 ({{ selectedDevices.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 移动设备对话框 -->
    <el-dialog
      title="移动设备到其他分组"
      v-model="moveDeviceDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="目标分组">
          <el-select v-model="targetGroupId" placeholder="请选择目标分组" style="width: 100%">
            <el-option
              v-for="group in availableTargetGroups"
              :key="group.id"
              :label="group.groupName"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveDeviceDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmMoveDevice"
            :disabled="!targetGroupId"
          >
            移动
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMonitoringGroupList,
  getMonitoringDevicesInGroup,
  createMonitoringGroup,
  updateMonitoringGroup,
  deleteMonitoringGroup,
  addMonitoringDevicesToGroup,
  removeMonitoringDevicesFromGroup,
  moveMonitoringDevicesToGroup,
  getAllMonitoringDevices,
  getUngroupedMonitoringDevices
} from '@/api/deviceGroup'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  Folder,
  FolderOpened,
  Monitor,
  Connection,
  Right,
  Search
} from '@element-plus/icons-vue'

export default {
  name: 'MonitoringGroupPreview',
  components: {
    Plus,
    Edit,
    Delete,
    Folder,
    FolderOpened,
    Monitor,
    Connection,
    Right,
    Search
  },
  data() {
    return {
      // 监控分组列表数据（平级结构）
      groupListData: [],
      // 当前选中的分组
      selectedGroup: null,
      // 当前分组的监控设备列表
      currentGroupDevices: [],
      // 所有可用监控设备列表
      allDevices: [],
      // 拖拽相关
      draggedDevice: null,

      // 监控分屏相关
      selectedScreenSplit: 9, // 默认9分屏
      screenSplitOptions: [
        { value: 1, label: '1分屏', cols: 1, rows: 1 },
        { value: 4, label: '4分屏', cols: 2, rows: 2 },
        { value: 6, label: '6分屏', cols: 3, rows: 2 },
        { value: 9, label: '9分屏', cols: 3, rows: 3 }
      ],

      // 分组对话框
      groupDialogVisible: false,
      groupDialogTitle: '新增监控分组',
      groupForm: {
        id: '',
        name: '',
        description: ''
      },
      groupFormRules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      isEditMode: false,

      // 添加设备对话框
      addDeviceDialogVisible: false,
      availableDevices: [],
      selectedDevices: [],
      deviceSearchKeyword: '',

      // 移动设备对话框
      moveDeviceDialogVisible: false,
      deviceToMove: null,
      targetGroupId: '',

      // 加载状态
      loading: false
    }
  },
  computed: {
    // 过滤后的可用设备列表
    filteredAvailableDevices() {
      if (!this.deviceSearchKeyword) {
        return this.availableDevices
      }
      const keyword = this.deviceSearchKeyword.toLowerCase()
      return this.availableDevices.filter(device =>
        device.name.toLowerCase().includes(keyword) ||
        device.deviceNo.toLowerCase().includes(keyword)
      )
    },
    // 可选择的目标分组列表
    availableTargetGroups() {
      return this.groupListData.filter(group =>
        group.id !== this.selectedGroup?.id
      )
    },

    // 当前分屏配置
    currentScreenConfig() {
      return this.screenSplitOptions.find(option => option.value === this.selectedScreenSplit)
    },

    // 显示的设备列表（根据分屏数量限制）
    displayDevices() {
      return this.currentGroupDevices.slice(0, this.selectedScreenSplit)
    },

    // 需要填充的空白格子数量
    emptySlots() {
      const deviceCount = this.currentGroupDevices.length
      const maxSlots = this.selectedScreenSplit
      return Math.max(0, maxSlots - deviceCount)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadGroupList(),
          // this.loadAllDevices()
        ])
        // 默认选中未分组
        const ungroupedNode = this.groupListData.find(group => group.id === '0')
        if (ungroupedNode) {

          this.handleGroupClick(ungroupedNode)
        }
      } catch (error) {
        ElMessage.error('数据加载失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 加载监控分组列表数据
    async loadGroupList() {
      try {
        const response = await getMonitoringGroupList()
        if (response.data.code === 200) {
          this.groupListData = response.data.data.records
          console.log('groupListData', this.groupListData);
          this.groupListData.unshift({
            id: '0',
            groupName: '未分组',
          })
        } else {
          // 如果API未实现，使用模拟数据
          this.groupListData = this.getMockGroupData()
        }
      } catch (error) {
        console.warn('API未实现，使用模拟数据')
        this.groupListData = this.getMockGroupData()
      }
    },

    // 加载所有监控设备数据
    async loadAllDevices() {
      try {
        const response = await getAllMonitoringDevices()
        if (response.data.code === 200) {
          this.allDevices = response.data.data
          
        } else {
          // 如果API未实现，使用模拟数据
          this.allDevices = this.getMockDeviceData()
        }
      } catch (error) {
        console.warn('API未实现，使用模拟数据')
        this.allDevices = this.getMockDeviceData()
      }
    },

    // 获取模拟监控分组数据（平级结构）
    getMockGroupData() {
      return [
        {
          id: 'ungrouped',
          name: '未分组',
          description: '未分配到具体分组的监控设备',
          deviceCount: 3,
          isDefault: true,
          deletable: false
        },
        {
          id: 'group_001',
          name: '大厅监控',
          description: '大厅区域的监控摄像头',
          deviceCount: 4,
          isDefault: false,
          deletable: true
        },
        {
          id: 'group_002',
          name: '办公区监控',
          description: '办公区域的监控摄像头',
          deviceCount: 6,
          isDefault: false,
          deletable: true
        },
        {
          id: 'group_003',
          name: '室外监控',
          description: '室外区域的监控摄像头',
          deviceCount: 5,
          isDefault: false,
          deletable: true
        },
        {
          id: 'group_004',
          name: '停车场监控',
          description: '停车场区域的监控摄像头',
          deviceCount: 3,
          isDefault: false,
          deletable: true
        }
      ]
    },

    // 获取模拟监控设备数据（仅摄像头）
    getMockDeviceData() {
      return [
        // 未分组监控设备
        { id: 'camera_001', name: '临时摄像头1', deviceNo: 'TEMP001', type: 'camera', status: 'online', location: '临时位置1', resolution: '1080P', brand: '海康威视', groupId: 'ungrouped' },
        { id: 'camera_002', name: '临时摄像头2', deviceNo: 'TEMP002', type: 'camera', status: 'offline', location: '临时位置2', resolution: '720P', brand: '大华', groupId: 'ungrouped' },
        { id: 'camera_003', name: '备用摄像头', deviceNo: 'BACKUP001', type: 'camera', status: 'online', location: '备用位置', resolution: '4K', brand: '宇视', groupId: 'ungrouped' },

        // 大厅监控设备
        { id: 'camera_004', name: '大厅入口摄像头', deviceNo: 'HALL001', type: 'camera', status: 'online', location: '大厅入口', resolution: '1080P', brand: '海康威视', groupId: 'group_001' },
        { id: 'camera_005', name: '大厅中央摄像头', deviceNo: 'HALL002', type: 'camera', status: 'online', location: '大厅中央', resolution: '4K', brand: '海康威视', groupId: 'group_001' },
        { id: 'camera_006', name: '大厅出口摄像头', deviceNo: 'HALL003', type: 'camera', status: 'online', location: '大厅出口', resolution: '1080P', brand: '大华', groupId: 'group_001' },
        { id: 'camera_007', name: '大厅服务台摄像头', deviceNo: 'HALL004', type: 'camera', status: 'offline', location: '服务台', resolution: '1080P', brand: '宇视', groupId: 'group_001' },

        // 办公区监控设备
        { id: 'camera_008', name: '办公区走廊1', deviceNo: 'OFFICE001', type: 'camera', status: 'online', location: '办公区走廊1', resolution: '1080P', brand: '海康威视', groupId: 'group_002' },
        { id: 'camera_009', name: '办公区走廊2', deviceNo: 'OFFICE002', type: 'camera', status: 'online', location: '办公区走廊2', resolution: '1080P', brand: '海康威视', groupId: 'group_002' },
        { id: 'camera_010', name: '会议室A摄像头', deviceNo: 'MEET001', type: 'camera', status: 'online', location: '会议室A', resolution: '4K', brand: '大华', groupId: 'group_002' },
        { id: 'camera_011', name: '会议室B摄像头', deviceNo: 'MEET002', type: 'camera', status: 'offline', location: '会议室B', resolution: '1080P', brand: '宇视', groupId: 'group_002' },
        { id: 'camera_012', name: '办公区电梯口', deviceNo: 'OFFICE003', type: 'camera', status: 'online', location: '电梯口', resolution: '1080P', brand: '海康威视', groupId: 'group_002' },
        { id: 'camera_013', name: '办公区茶水间', deviceNo: 'OFFICE004', type: 'camera', status: 'online', location: '茶水间', resolution: '720P', brand: '大华', groupId: 'group_002' },

        // 室外监控设备
        { id: 'camera_014', name: '主入口摄像头', deviceNo: 'OUTDOOR001', type: 'camera', status: 'online', location: '主入口', resolution: '4K', brand: '海康威视', groupId: 'group_003' },
        { id: 'camera_015', name: '侧门摄像头', deviceNo: 'OUTDOOR002', type: 'camera', status: 'online', location: '侧门', resolution: '1080P', brand: '大华', groupId: 'group_003' },
        { id: 'camera_016', name: '花园摄像头', deviceNo: 'OUTDOOR003', type: 'camera', status: 'offline', location: '花园', resolution: '1080P', brand: '宇视', groupId: 'group_003' },
        { id: 'camera_017', name: '围墙摄像头1', deviceNo: 'OUTDOOR004', type: 'camera', status: 'online', location: '围墙东侧', resolution: '1080P', brand: '海康威视', groupId: 'group_003' },
        { id: 'camera_018', name: '围墙摄像头2', deviceNo: 'OUTDOOR005', type: 'camera', status: 'online', location: '围墙西侧', resolution: '1080P', brand: '大华', groupId: 'group_003' },

        // 停车场监控设备
        { id: 'camera_019', name: '停车场入口', deviceNo: 'PARK001', type: 'camera', status: 'online', location: '停车场入口', resolution: '4K', brand: '海康威视', groupId: 'group_004' },
        { id: 'camera_020', name: '停车场A区', deviceNo: 'PARK002', type: 'camera', status: 'online', location: '停车场A区', resolution: '1080P', brand: '大华', groupId: 'group_004' },
        { id: 'camera_021', name: '停车场B区', deviceNo: 'PARK003', type: 'camera', status: 'offline', location: '停车场B区', resolution: '1080P', brand: '宇视', groupId: 'group_004' }
      ]
    },

    // 分组点击事件
    async handleGroupClick(group) {
      this.selectedGroup = group
      if (group.id === '0') {
        group.id = 'groupIdsNull'
      }
      await this.loadGroupDevices(group.id)
    },

    // 加载分组内的设备
    async loadGroupDevices(groupId) {
      try {
        // 从模拟数据中筛选设备
        let row  = await getMonitoringDevicesInGroup(groupId)
        console.log(row,"<<<<<<<<<<<<<<<<<<<<<<<<<");
        
        this.currentGroupDevices = row.data.data.records
        // 更新分组的设备数量
        this.updateGroupDeviceCount(groupId, this.currentGroupDevices.length)
      } catch (error) {
        ElMessage.error('加载设备列表失败：' + error.message)
      }
    },

    // 更新分组设备数量
    updateGroupDeviceCount(groupId, count) {
      const group = this.groupListData.find(g => g.id === groupId)
      if (group) {
        group.deviceCount = count
      }
    },

    // 分组右键菜单
    handleGroupRightClick(data) {
      // 可以在这里添加右键菜单功能
      console.log('右键点击分组:', data)
    },

    // 显示新增分组对话框
    showAddGroupDialog() {
      this.isEditMode = false
      this.groupDialogTitle = '新增监控分组'
      this.groupForm = {
        id: '',
        name: '',
        description: ''
      }
      this.groupDialogVisible = true
    },

    // 编辑分组
    editGroup(group) {
      console.log('编辑分组:', group)
      this.isEditMode = true
      this.groupDialogTitle = '编辑监控分组'
      this.groupForm = {
        name: group.groupName,
        description:  group.remark
        id: group.id,
      }
      this.groupDialogVisible = true
    },

    // 删除分组
    async deleteGroup(group) {
      if (group.deviceCount > 0) {
        ElMessage.warning('该分组下还有设备，无法删除')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要删除分组"${group.name}"吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 这里调用删除API
        // await deleteDeviceGroup(group.id)

        // 从分组列表中移除
        this.removeGroupFromList(group.id)
        ElMessage.success('删除成功')

        // 如果删除的是当前选中的分组，重新选择未分组
        if (this.selectedGroup?.id === group.id) {
          const ungroupedNode = this.groupListData.find(g => g.id === 'ungrouped')
          if (ungroupedNode) {
            this.handleGroupClick(ungroupedNode)
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败：' + error.message)
        }
      }
    },

    // 从分组列表中移除分组
    removeGroupFromList(groupId) {
      const index = this.groupListData.findIndex(group => group.id === groupId)
      if (index > -1) {
        this.groupListData.splice(index, 1)
      }
    },

    // 提交分组表单
    async submitGroupForm() {
      try {
        await this.$refs.groupForm.validate()

        if (this.isEditMode) {
          this.groupForm = {
            id: this.groupForm.id,
            groupName: this.groupForm.name,
            remark: this.groupForm.description
          }
          // 编辑分组
          await updateMonitoringGroup(this.groupForm)
          // this.updateGroupInList(this.groupForm)
          ElMessage.success('编辑成功')
        } else {
          // 新增分组
          const newGroup = {
            groupName: this.groupForm.name,
            remark: this.groupForm.description
          }

          await createMonitoringGroup(newGroup)
          // this.addGroupToList(newGroup)
          ElMessage.success('新增成功')
        }
        this.groupDialogVisible = false
      } catch (error) {
        if (error !== false) {
          ElMessage.error('操作失败：' + error.message)
        }
      }
    },

    // 更新分组列表中的分组
    updateGroupInList(groupData) {
      const group = this.groupListData.find(g => g.id === groupData.id)
      if (group) {
        group.name = groupData.name
        group.description = groupData.description
      }
    },

    // 添加分组到分组列表
    addGroupToList(newGroup) {
      this.groupListData.push(newGroup)
    },

    // 显示添加设备对话框
    async showAddDeviceDialog() {
      if (!this.selectedGroup) {
        ElMessage.warning('请先选择一个分组')
        return
      }

      // 获取可添加的设备（未分组的设备或其他分组的设备）
      this.availableDevices = this.allDevices.filter(device =>
        device.groupId !== this.selectedGroup.id
      )

      this.selectedDevices = []
      this.deviceSearchKeyword = ''
      this.addDeviceDialogVisible = true
    },

    // 设备选择变化
    handleDeviceSelectionChange(selection) {
      this.selectedDevices = selection
    },

    // 添加选中的设备到分组
    async addSelectedDevicesToGroup() {
      if (this.selectedDevices.length === 0) {
        ElMessage.warning('请选择要添加的设备')
        return
      }

      try {
        // 更新设备的分组ID
        this.selectedDevices.forEach(device => {
          device.groupId = this.selectedGroup.id
        })

        // 重新加载当前分组的设备
        await this.loadGroupDevices(this.selectedGroup.id)

        ElMessage.success(`成功添加 ${this.selectedDevices.length} 个设备`)
        this.addDeviceDialogVisible = false
      } catch (error) {
        ElMessage.error('添加设备失败：' + error.message)
      }
    },

    // 显示移动设备对话框
    showMoveDeviceDialog(device) {
      this.deviceToMove = device
      this.targetGroupId = ''
      this.moveDeviceDialogVisible = true
    },

    // 确认移动设备
    async confirmMoveDevice() {
      if (!this.targetGroupId) {
        ElMessage.warning('请选择目标分组')
        return
      }

      try {
        // 更新设备的分组ID
        this.deviceToMove.groupId = this.targetGroupId

        // 重新加载当前分组的设备
        await this.loadGroupDevices(this.selectedGroup.id)

        // 更新目标分组的设备数量
        const targetDevices = this.allDevices.filter(d => d.groupId === this.targetGroupId)
        this.updateGroupDeviceCount(this.targetGroupId, targetDevices.length)

        ElMessage.success('设备移动成功')
        this.moveDeviceDialogVisible = false
      } catch (error) {
        ElMessage.error('移动设备失败：' + error.message)
      }
    },

    // 从分组中移除设备
    async removeDeviceFromGroup(device) {
      try {
        await ElMessageBox.confirm(
          `确定要将设备"${device.name}"移动到未分组吗？`,
          '确认移动',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 将设备移动到未分组
        device.groupId = 'ungrouped'

        // 重新加载当前分组的设备
        await this.loadGroupDevices(this.selectedGroup.id)

        // 更新未分组的设备数量
        const ungroupedDevices = this.allDevices.filter(d => d.groupId === 'ungrouped')
        this.updateGroupDeviceCount('ungrouped', ungroupedDevices.length)

        ElMessage.success('设备已移动到未分组')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('移除设备失败：' + error.message)
        }
      }
    },

    // 拖拽开始
    handleDragStart(device, event) {
      this.draggedDevice = device
      event.dataTransfer.effectAllowed = 'move'
    },

    // 拖拽放置
    async handleDrop(event) {
      event.preventDefault()
      if (!this.draggedDevice) return

      // 这里可以根据放置的位置来确定目标分组
      // 简化处理：显示移动设备对话框
      this.showMoveDeviceDialog(this.draggedDevice)
      this.draggedDevice = null
    },

    // 选择设备（用于监控预览）
    selectDevice(device) {
      console.log('选择监控设备进行预览:', device)
      // 这里可以添加监控设备预览的逻辑
    },

    // 处理分屏切换
    handleScreenSplitChange(value) {
      console.log('切换分屏数量:', value)
      // 这里可以添加分屏切换的额外逻辑
    },

    // 获取网格样式类
    getGridClass() {
      const config = this.currentScreenConfig
      if (!config) return 'grid-3x3'

      return `grid-${config.cols}x${config.rows}`
    }
  }
}
</script>

<style lang="scss" scoped>
.preview {
  padding: 10px;
  height: calc(100vh - 120px);
}

// 监控分组管理区域
.group-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;

  .group-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .group-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: white;
    }

    .el-button {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .group-list-container {
    flex: 1;
    padding: 8px;
    overflow: auto;

    .group-list {
      .group-item {
        margin-bottom: 8px;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        &.active {
          border-color: #409eff;
          background: #f0f9ff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .group-content {
          padding: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .group-info {
            display: flex;
            align-items: center;
            flex: 1;

            .group-icon {
              margin-right: 12px;
              color: #409eff;
              font-size: 18px;
            }

            .group-details {
              flex: 1;

              .group-name {
                font-size: 14px;
                color: #333;
                font-weight: 500;
                margin-bottom: 4px;
              }

              .group-desc {
                font-size: 12px;
                color: #999;
                line-height: 1.2;
              }
            }

            .device-count {
              margin-left: 12px;
            }
          }

          .group-actions {
            display: none;

            .el-button {
              padding: 4px;
              margin-left: 4px;
            }
          }
        }

        &:hover .group-actions {
          display: flex;
        }
      }
    }
  }

  .group-devices {
    border-top: 1px solid #f0f0f0;

    .devices-header {
      padding: 12px 16px;
      background: #fafafa;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 0;
        font-size: 14px;
        color: #333;
      }
    }

    .devices-list {
      padding: 8px;

      .device-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f9f9f9;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        cursor: move;
        transition: all 0.3s;

        &:hover {
          background: #f0f9ff;
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .device-info {
          display: flex;
          align-items: center;
          flex: 1;

          .device-icon {
            margin-right: 8px;
            color: #67c23a;
          }

          .device-details {
            flex: 1;

            .device-name {
              font-size: 13px;
              color: #333;
              font-weight: 500;
              margin-bottom: 4px;
            }

            .device-meta {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .device-no, .device-location, .device-resolution {
                font-size: 11px;
                color: #999;
              }

              .device-location {
                color: #666;
              }

              .device-resolution {
                color: #409eff;
                font-weight: 500;
              }
            }
          }

          .device-status {
            margin-left: 8px;
          }
        }

        .device-actions {
          display: none;

          .el-button {
            padding: 4px;
            margin-left: 4px;
          }
        }

        &:hover .device-actions {
          display: flex;
        }
      }
    }
  }
}

// 监控预览区域
.monitoring-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 738px;
  .monitoring-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .monitoring-header-left {
      flex: 1;

      h3 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .monitoring-info {
        font-size: 12px;
        color: #666;
      }
    }

    .monitoring-header-right {
      flex-shrink: 0;

      .screen-split-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .control-label {
          font-size: 14px;
          color: #666;
          white-space: nowrap;
        }

        .el-radio-group {
          .el-radio-button {
            .el-radio-button__inner {
              padding: 8px 16px;
              font-size: 12px;
              border-radius: 4px;
              transition: all 0.3s;

              &:hover {
                color: #409eff;
                border-color: #409eff;
              }
            }

            &.is-active {
              .el-radio-button__inner {
                background: #409eff;
                border-color: #409eff;
                color: white;
                box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
              }
            }
          }
        }
      }
    }
  }

  .monitoring-content {
    flex: 1;
    padding: 16px;

    .monitoring-grid {
      display: grid;
      gap: 12px;
      height: 100%;

      // 1分屏 (1x1)
      &.grid-1x1 {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
      }

      // 4分屏 (2x2)
      &.grid-2x2 {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
      }

      // 6分屏 (3x2)
      &.grid-3x2 {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
      }

      // 9分屏 (3x3) - 默认
      &.grid-3x3 {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
      }

      .monitoring-item {
        background: #f8f9fa;
        border: 2px dashed #ddd;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        min-height: 120px;
        position: relative;

        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }

        &.empty {
          opacity: 0.5;

          &:hover {
            border-color: #67c23a;
            background: #f0f9ff;
          }
        }

        .monitor-placeholder {
          text-align: center;
          width: 100%;

          .device-label {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
            font-weight: 500;
          }

          .device-status-badge {
            position: absolute;
            top: 8px;
            right: 8px;

            .el-tag {
              font-size: 10px;
              padding: 2px 6px;
            }
          }
        }
      }
    }
  }
}

// 对话框样式
.add-device-content {
  .el-table {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .monitoring-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .monitoring-header-right {
      width: 100%;

      .screen-split-controls {
        justify-content: flex-start;
        flex-wrap: wrap;

        .el-radio-group {
          .el-radio-button {
            .el-radio-button__inner {
              padding: 6px 12px;
              font-size: 11px;
            }
          }
        }
      }
    }
  }

  .monitoring-grid {
    // 在中等屏幕上，限制最大分屏数
    &.grid-3x3 {
      grid-template-columns: repeat(2, 1fr) !important;
      grid-template-rows: repeat(2, 1fr) !important;
    }

    &.grid-3x2 {
      grid-template-columns: repeat(2, 1fr) !important;
      grid-template-rows: repeat(2, 1fr) !important;
    }
  }
}

@media (max-width: 768px) {
  .preview {
    padding: 5px;
  }

  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .devices-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .monitoring-header {
    padding: 12px;

    .monitoring-header-left {
      h3 {
        font-size: 14px;
      }

      .monitoring-info {
        font-size: 11px;
      }
    }

    .monitoring-header-right {
      .screen-split-controls {
        .control-label {
          font-size: 12px;
        }

        .el-radio-group {
          .el-radio-button {
            .el-radio-button__inner {
              padding: 4px 8px;
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .monitoring-grid {
    // 在小屏幕上，强制使用单列布局
    grid-template-columns: 1fr !important;
    grid-template-rows: auto !important;

    .monitoring-item {
      min-height: 100px;
    }
  }
}
</style>
