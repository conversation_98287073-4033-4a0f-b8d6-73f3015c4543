/**
 * 设备分组管理相关API
 */
import request from '@/axios'

/**
 * 获取设备分组列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回设备分组列表
 */
export const getDeviceGroupList = (params = {}) => {
  return request({
    url: '/api/device-group/list',
    method: 'get',
    params
  })
}

/**
 * 获取监控设备分组列表（平级结构）
 * @returns {Promise} 返回监控设备分组列表数据
 */
export const getMonitoringGroupList = () => {
  return request({
    url: '/SurveillanceSecurityGroup/page',
    method: 'get'
  })
}

/**
 * 创建监控设备分组
 * @param {Object} data - 分组数据
 * @param {string} data.name - 分组名称
 * @param {string} data.description - 分组描述
 * @returns {Promise} 返回创建结果
 */
export const createMonitoringGroup = (data) => {
  return request({
    url: '/SurveillanceSecurityGroup/save',
    method: 'post',
    data
  })
}

/**
 * 更新监控设备分组
 * @param {Object} data - 分组数据
 * @param {string} data.id - 分组ID
 * @param {string} data.name - 分组名称
 * @param {string} data.description - 分组描述
 * @returns {Promise} 返回更新结果
 */
export const updateMonitoringGroup = (data) => {
  return request({
    url: '/SurveillanceSecurityGroup/update',
    method: 'post',
    data
  })
}

/**
 * 删除监控设备分组
 * @param {string} id - 分组ID
 * @returns {Promise} 返回删除结果
 */
export const deleteMonitoringGroup = (id) => {
  return request({
    url: '/SurveillanceSecurityGroup/removeById',
    method: 'post',
    params: { id }
  })
}

/**
 * 获取分组内的监控设备列表
 * @param {string} groupId - 分组ID
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回监控设备列表
 */
export const getMonitoringDevicesInGroup = (row) => {
  return request({
    url: '/monitoringEquipment/page',
    method: 'get',
    params: {
      ...row
    }
  })
}

/**
 * 将监控设备添加到分组
 * @param {Object} data - 操作数据
 * @param {string} data.groupId - 分组ID
 * @param {Array} data.deviceIds - 监控设备ID数组
 * @returns {Promise} 返回操作结果
 */
export const addMonitoringDevicesToGroup = (data) => {
  return request({
    url: '/api/monitoring-group/add-devices',
    method: 'post',
    data
  })
}

/**
 * 从分组中移除监控设备
 * @param {Object} data - 操作数据
 * @param {string} data.groupId - 分组ID
 * @param {Array} data.deviceIds - 监控设备ID数组
 * @returns {Promise} 返回操作结果
 */
export const removeMonitoringDevicesFromGroup = (data) => {
  return request({
    url: '/api/monitoring-group/remove-devices',
    method: 'post',
    data
  })
}

/**
 * 移动监控设备到其他分组
 * @param {Object} data - 操作数据
 * @param {string} data.fromGroupId - 源分组ID
 * @param {string} data.toGroupId - 目标分组ID
 * @param {Array} data.deviceIds - 监控设备ID数组
 * @returns {Promise} 返回操作结果
 */
export const moveMonitoringDevicesToGroup = (data) => {
  return request({
    url: '/api/monitoring-group/move-devices',
    method: 'post',
    data
  })
}

/**
 * 获取所有监控设备列表（用于分组管理）
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回监控设备列表
 */
export const getAllMonitoringDevices = (params = {}) => {
  return request({
    url: '/api/monitoring-devices/all',
    method: 'get',
    params
  })
}

/**
 * 获取未分组的监控设备列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回未分组监控设备列表
 */
export const getUngroupedMonitoringDevices = (params = {}) => {
  return request({
    url: '/api/monitoring-devices/ungrouped',
    method: 'get',
    params
  })
}

// API响应数据格式示例

/**
 * 监控设备分组列表响应格式（平级结构）
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": [
 *     {
 *       "id": "ungrouped",
 *       "name": "未分组",
 *       "description": "未分配到具体分组的监控设备",
 *       "deviceCount": 8,
 *       "isDefault": true,
 *       "deletable": false,
 *       "createTime": "2025-01-30T10:00:00Z",
 *       "updateTime": "2025-01-30T10:00:00Z"
 *     },
 *     {
 *       "id": "group_001",
 *       "name": "大厅监控",
 *       "description": "大厅区域的监控摄像头",
 *       "deviceCount": 5,
 *       "isDefault": false,
 *       "deletable": true,
 *       "createTime": "2025-01-30T10:00:00Z",
 *       "updateTime": "2025-01-30T10:00:00Z"
 *     },
 *     {
 *       "id": "group_002",
 *       "name": "办公区监控",
 *       "description": "办公区域的监控摄像头",
 *       "deviceCount": 8,
 *       "isDefault": false,
 *       "deletable": true,
 *       "createTime": "2025-01-30T10:00:00Z",
 *       "updateTime": "2025-01-30T10:00:00Z"
 *     }
 *   ]
 * }
 */

/**
 * 分组内监控设备列表响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": [
 *     {
 *       "id": "camera_001",
 *       "name": "大厅1号摄像头",
 *       "deviceNo": "CAM001",
 *       "type": "camera",
 *       "status": "online",
 *       "location": "大厅入口",
 *       "resolution": "1080P",
 *       "brand": "海康威视",
 *       "model": "DS-2CD2T86FWDV2-I3S",
 *       "groupId": "group_001",
 *       "createTime": "2025-01-30T10:00:00Z"
 *     }
 *   ]
 * }
 */
